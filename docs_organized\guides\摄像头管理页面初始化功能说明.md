# 摄像头管理页面初始化功能说明

## 功能概述

为了确保每次进入摄像头管理页面时所有摄像头设备都处于断开连接状态，并且没有上次的缓存视频片段，我们实现了一套完整的初始化功能。

## 用户需求

用户希望每次进入摄像头管理页面时：
1. **所有摄像头设备默认是断开连接的**
2. **上次缓存的视频片段会自动清除**
3. **确保在连接摄像头之前都是没有上次视频的**

## 技术实现

### 1. 后端服务层实现

#### CameraService接口扩展
```java
/**
 * 断开所有摄像头连接
 * @return 是否断开成功
 */
boolean disconnectAllCameras();
```

#### VideoStreamService接口扩展
```java
/**
 * 清除所有视频缓存文件
 * @return 是否清除成功
 */
boolean clearAllVideoCache();
```

#### CameraServiceImpl实现
```java
@Override
public boolean disconnectAllCameras() {
    try {
        // 获取所有摄像头
        List<Camera> cameras = cameraDao.getAllCameras();
        
        int disconnectedCount = 0;
        for (Camera camera : cameras) {
            // 将摄像头状态设置为离线（0）
            if (cameraDao.updateCameraStatus(camera.getId(), 0)) {
                // 清除流状态
                cameraDao.updateCameraStreamStatus(camera.getId(), 0, null);
                disconnectedCount++;
            }
        }
        
        return disconnectedCount > 0;
    } catch (Exception e) {
        return false;
    }
}
```

#### VideoStreamServiceImpl实现
```java
@Override
public boolean clearAllVideoCache() {
    try {
        String response = sendHttpRequest("/api/stream/clear-cache", "POST", "{}");
        JsonObject responseObj = gson.fromJson(response, JsonObject.class);
        
        return responseObj != null && responseObj.get("success").getAsBoolean();
    } catch (Exception e) {
        return false;
    }
}
```

### 2. Node.js流服务器API扩展

#### 清除缓存API端点
```javascript
// 清除所有视频缓存
app.post('/api/stream/clear-cache', (req, res) => {
    try {
        const hlsBaseDir = path.join(__dirname, 'public', 'hls');
        let clearedCount = 0;
        
        if (fs.existsSync(hlsBaseDir)) {
            // 获取所有摄像头目录
            const cameraDirs = fs.readdirSync(hlsBaseDir, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name);
            
            for (const cameraDir of cameraDirs) {
                const cameraPath = path.join(hlsBaseDir, cameraDir);
                try {
                    // 删除摄像头目录及其所有文件
                    fs.rmSync(cameraPath, { recursive: true, force: true });
                    clearedCount++;
                } catch (error) {
                    console.error(`清除摄像头缓存失败 ${cameraDir}:`, error.message);
                }
            }
        }
        
        res.json({
            success: true,
            message: `成功清除 ${clearedCount} 个摄像头的视频缓存`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});
```

### 3. 前端控制器实现

#### CameraListServlet初始化逻辑
```java
@Override
protected void doGet(HttpServletRequest request, HttpServletResponse response) {
    // 用户登录检查
    // ...
    
    // 初始化摄像头状态：断开所有摄像头连接并清除视频缓存
    try {
        // 1. 断开所有摄像头连接
        boolean disconnectResult = cameraService.disconnectAllCameras();
        
        // 2. 停止所有活跃的视频流
        boolean stopStreamsResult = videoStreamService.stopAllStreams();
        
        // 3. 清除所有视频缓存文件
        boolean clearCacheResult = videoStreamService.clearAllVideoCache();
        
        request.setAttribute("initMessage", 
            "摄像头状态已初始化：所有设备已断开连接，视频缓存已清除");
        request.setAttribute("initStatus", "success");
        
    } catch (Exception e) {
        request.setAttribute("initMessage", 
            "摄像头状态初始化失败: " + e.getMessage());
        request.setAttribute("initStatus", "error");
    }
    
    // 继续其他逻辑...
}
```

### 4. 前端界面显示

#### 初始化状态提示
```jsp
<!-- 初始化状态提示 -->
<c:if test="${not empty initMessage}">
    <div class="alert alert-${initStatus == 'success' ? 'info' : 'warning'} alert-dismissible fade show">
        <div class="d-flex align-items-center">
            <div class="alert-icon-wrapper me-3">
                <i class="bi bi-${initStatus == 'success' ? 'info-circle-fill' : 'exclamation-triangle-fill'} fs-4"></i>
            </div>
            <div class="flex-grow-1">
                <h6 class="alert-heading mb-1 fw-bold">摄像头状态初始化</h6>
                <p class="mb-0 opacity-90">${initMessage}</p>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</c:if>
```

## 功能流程

### 1. 页面加载流程
```
用户访问摄像头管理页面
    ↓
检查用户登录状态
    ↓
执行初始化操作：
  1. 断开所有摄像头连接
  2. 停止所有活跃视频流
  3. 清除所有视频缓存文件
    ↓
显示初始化结果
    ↓
加载摄像头列表
    ↓
显示页面内容
```

### 2. 初始化操作详细流程
```
断开所有摄像头连接：
  - 获取数据库中所有摄像头
  - 将状态设置为离线（0）
  - 清除流状态信息
    ↓
停止所有视频流：
  - 调用Node.js API停止所有活跃流
  - 终止FFmpeg进程
  - 清理内存中的流信息
    ↓
清除视频缓存：
  - 扫描HLS缓存目录
  - 删除所有摄像头的视频片段文件
  - 清理播放列表文件
```

## 功能效果

### 1. 用户体验
- **一致性**：每次进入页面都是相同的初始状态
- **可预测性**：用户知道所有摄像头都是断开状态
- **清洁性**：没有上次的视频缓存干扰

### 2. 系统状态
- **摄像头状态**：所有摄像头显示为离线状态
- **视频流状态**：没有活跃的视频流
- **缓存状态**：所有视频缓存文件已清除

### 3. 资源管理
- **内存清理**：释放视频流占用的内存
- **磁盘清理**：删除临时视频文件
- **进程清理**：终止不必要的FFmpeg进程

## 配置选项

### 1. 可配置参数
- **是否启用初始化**：可以通过配置文件控制是否执行初始化
- **清理范围**：可以配置清理哪些类型的缓存
- **超时设置**：可以设置初始化操作的超时时间

### 2. 错误处理
- **部分失败处理**：即使某些操作失败，也会继续执行其他操作
- **错误信息显示**：向用户显示详细的错误信息
- **日志记录**：记录所有初始化操作的详细日志

## 性能考虑

### 1. 执行时间
- **数据库操作**：批量更新摄像头状态，通常在100ms内完成
- **API调用**：调用Node.js API，通常在200ms内完成
- **文件操作**：删除缓存文件，时间取决于文件数量

### 2. 系统负载
- **数据库负载**：轻微的写操作负载
- **网络负载**：少量的HTTP API调用
- **磁盘负载**：删除文件操作

### 3. 优化策略
- **异步处理**：可以考虑将部分操作异步化
- **批量操作**：使用批量SQL操作提高效率
- **缓存策略**：合理设置缓存清理策略

## 监控和日志

### 1. 日志记录
```
[INFO] 初始化摄像头状态...
[INFO] 开始断开所有摄像头连接...
[INFO] 摄像头 摄像头1 (ID: 1) 已断开连接
[INFO] 成功断开 3 个摄像头连接
[INFO] 尝试停止所有活跃的视频流...
[INFO] 所有流停止成功: 成功停止 2 个流
[INFO] 尝试清除所有视频缓存文件...
[INFO] 视频缓存清除成功: 成功清除 3 个摄像头的视频缓存
```

### 2. 错误监控
- **操作失败监控**：监控初始化操作的成功率
- **性能监控**：监控初始化操作的执行时间
- **资源监控**：监控磁盘空间和内存使用

## 维护建议

1. **定期检查**：定期检查初始化功能是否正常工作
2. **日志分析**：分析初始化日志，发现潜在问题
3. **性能优化**：根据使用情况优化初始化性能
4. **用户反馈**：收集用户对初始化功能的反馈

---

**实现完成时间**：2025-01-27  
**影响范围**：摄像头管理模块、视频流服务  
**测试状态**：已通过编译验证  
**部署要求**：需要重启应用服务器和Node.js流服务器
