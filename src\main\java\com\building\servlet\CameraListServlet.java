package com.building.servlet;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.Camera;
import com.building.model.Room;
import com.building.service.CameraService;
import com.building.service.RoomService;
import com.building.service.VideoStreamService;
import com.building.service.impl.CameraServiceImpl;
import com.building.service.impl.RoomServiceImpl;
import com.building.service.impl.VideoStreamServiceImpl;

/**
 * 摄像头列表Servlet
 * 用于处理摄像头列表页面的请求
 */
@WebServlet("/camera/list")
public class CameraListServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private CameraService cameraService;
    private RoomService roomService;
    private VideoStreamService videoStreamService;

    @Override
    public void init() throws ServletException {
        try {
            cameraService = new CameraServiceImpl();
            roomService = new RoomServiceImpl();
            videoStreamService = new VideoStreamServiceImpl();
            System.out.println("CameraListServlet 初始化成功");
        } catch (Exception e) {
            System.err.println("CameraListServlet 初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw new ServletException("初始化服务失败", e);
        }
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }

        // 初始化摄像头状态：断开所有摄像头连接并清除视频缓存
        try {
            System.out.println("初始化摄像头状态...");

            // 1. 断开所有摄像头连接
            boolean disconnectResult = cameraService.disconnectAllCameras();
            if (disconnectResult) {
                System.out.println("所有摄像头已断开连接");
            } else {
                System.out.println("断开摄像头连接时出现问题");
            }

            // 2. 停止所有活跃的视频流
            boolean stopStreamsResult = videoStreamService.stopAllStreams();
            if (stopStreamsResult) {
                System.out.println("所有视频流已停止");
            } else {
                System.out.println("停止视频流时出现问题");
            }

            // 3. 清除所有视频缓存文件
            boolean clearCacheResult = videoStreamService.clearAllVideoCache();
            if (clearCacheResult) {
                System.out.println("所有视频缓存已清除");
            } else {
                System.out.println("清除视频缓存时出现问题");
            }

            request.setAttribute("initMessage", "摄像头状态已初始化：所有设备已断开连接，视频缓存已清除");
            request.setAttribute("initStatus", "success");

        } catch (Exception e) {
            System.err.println("初始化摄像头状态时发生异常: " + e.getMessage());
            e.printStackTrace();
            request.setAttribute("initMessage", "摄像头状态初始化失败: " + e.getMessage());
            request.setAttribute("initStatus", "error");
        }

        // 自动启动流服务器（如果未运行且用户未手动停止）
        try {
            System.out.println("检查流服务器状态...");
            boolean isRunning = videoStreamService.isStreamServerRunning();
            boolean userManuallyStopped = videoStreamService.isUserManuallyStopped();

            if (!isRunning && !userManuallyStopped) {
                System.out.println("流服务器未运行且用户未手动停止，尝试启动...");
                boolean startResult = videoStreamService.startStreamServer();
                if (startResult) {
                    System.out.println("流服务器启动成功");
                    request.setAttribute("streamServerMessage", "视频流服务器已自动启动");
                    request.setAttribute("streamServerStatus", "success");
                } else {
                    System.err.println("流服务器启动失败");
                    request.setAttribute("streamServerMessage", "视频流服务器启动失败，视频功能可能不可用");
                    request.setAttribute("streamServerStatus", "warning");
                }
            } else if (!isRunning && userManuallyStopped) {
                System.out.println("流服务器未运行，但用户已手动停止，不自动启动");
                request.setAttribute("streamServerMessage", "视频流服务器已停止（用户手动停止）");
                request.setAttribute("streamServerStatus", "info");
            } else {
                System.out.println("流服务器已在运行中");
                request.setAttribute("streamServerMessage", "视频流服务器运行正常");
                request.setAttribute("streamServerStatus", "success");
            }
        } catch (Exception e) {
            System.err.println("检查或启动流服务器时发生异常: " + e.getMessage());
            e.printStackTrace();
            request.setAttribute("streamServerMessage", "视频流服务器检查失败: " + e.getMessage());
            request.setAttribute("streamServerStatus", "error");
        }

        // 获取摄像头列表
        List<Camera> cameras = cameraService.getAllCameras();
        request.setAttribute("cameras", cameras);

        // 获取房间列表（用于添加摄像头表单）
        List<Room> rooms = roomService.getAllRooms();
        request.setAttribute("rooms", rooms);

        // 获取摄像头统计信息
        Map<String, Integer> stats = cameraService.getCameraStats();
        request.setAttribute("cameraStats", stats);

        // 获取流服务器状态信息
        try {
            boolean isStreamServerRunning = videoStreamService.isStreamServerRunning();
            String healthDetails = videoStreamService.getStreamServerHealthDetails();
            request.setAttribute("streamServerRunning", isStreamServerRunning);
            request.setAttribute("streamServerHealth", healthDetails);
        } catch (Exception e) {
            System.err.println("获取流服务器状态失败: " + e.getMessage());
            request.setAttribute("streamServerRunning", false);
            request.setAttribute("streamServerHealth", "无法获取状态: " + e.getMessage());
        }

        // 转发到摄像头列表页面
        request.getRequestDispatcher("/WEB-INF/views/camera/list.jsp").forward(request, response);
    }
}
