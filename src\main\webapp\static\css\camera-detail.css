/* 摄像头详情页面专用样式 */

/* 基础变量 */
:root {
    --camera-primary: #007bff;
    --camera-secondary: #6c757d;
    --camera-success: #28a745;
    --camera-info: #17a2b8;
    --camera-warning: #ffc107;
    --camera-danger: #dc3545;
    --camera-light: #f8f9fa;
    --camera-dark: #343a40;
    --camera-border-radius: 12px;
    --camera-box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    --camera-transition: all 0.3s ease;
}

/* 页面整体布局优化 */
.container-fluid {
    max-width: 1400px;
}

/* 摄像头标题区域 */
.camera-header-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: var(--camera-border-radius);
    padding: 1rem 1.5rem;
    box-shadow: var(--camera-box-shadow);
    border: 1px solid rgba(0,0,0,0.05);
}

.camera-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--camera-dark);
    margin-bottom: 0.5rem;
}

.camera-location {
    font-size: 0.95rem;
    line-height: 1.4;
}

/* 操作按钮样式 */
.camera-action-btn {
    border-radius: 25px;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: var(--camera-transition);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.camera-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 摄像头状态指示器 */
.camera-status {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 10px;
    position: relative;
    animation: pulse 2s infinite;
}

.camera-online {
    background-color: var(--camera-success);
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
}

.camera-offline {
    background-color: var(--camera-danger);
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

/* 卡片通用样式 */
.card {
    border: none;
    border-radius: var(--camera-border-radius);
    box-shadow: var(--camera-box-shadow);
    transition: var(--camera-transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

/* 渐变背景 */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--camera-primary) 0%, #0056b3 100%);
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, var(--camera-secondary) 0%, #495057 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--camera-info) 0%, #138496 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--camera-success) 0%, #1e7e34 100%);
}

/* 视频流卡片 */
.camera-stream-card {
    margin-bottom: 1.5rem;
}

.camera-stream-container {
    position: relative;
    background-color: #000;
    border-radius: 8px;
    overflow: hidden;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stream-image {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
    border-radius: 8px;
}

.stream-overlay {
    text-align: center;
    color: #6c757d;
    padding: 2rem;
}

.stream-overlay h5 {
    margin-top: 1rem;
    font-weight: 600;
}

/* 状态徽章 */
.status-badge {
    font-size: 0.85rem;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
}

/* 控制按钮 */
.camera-control-btn {
    border-radius: 20px;
    padding: 0.4rem 1rem;
    font-weight: 500;
    transition: var(--camera-transition);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.camera-control-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 控制面板 */
.camera-control-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.control-grid {
    max-width: 200px;
    margin: 0 auto;
}

.control-row {
    display: flex;
    align-items: center;
}

.control-btn {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: var(--camera-transition);
    border: 2px solid;
}

.control-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.zoom-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
}

/* 信息列表样式 */
.camera-info-list {
    padding: 0.5rem 0;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    background-color: rgba(0,0,0,0.02);
    border-radius: 8px;
    transition: var(--camera-transition);
}

.info-item:hover {
    background-color: rgba(0,123,255,0.05);
    transform: translateX(5px);
}

.info-label {
    font-weight: 600;
    color: var(--camera-dark);
    font-size: 0.9rem;
    min-width: 100px;
    display: flex;
    align-items: center;
}

.info-value {
    text-align: right;
    color: var(--camera-secondary);
    font-size: 0.9rem;
    word-break: break-all;
    max-width: 60%;
}

/* 人员记录样式 */
.current-count-card {
    border-radius: 12px;
    padding: 1rem;
    background: linear-gradient(135deg, var(--camera-success) 0%, #1e7e34 100%);
}

.person-count-display {
    text-align: center;
}

.count-number {
    font-size: 2rem;
    font-weight: 800;
    line-height: 1;
}

.chart-container {
    height: 180px;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

.section-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--camera-dark);
    border-bottom: 2px solid var(--camera-light);
    padding-bottom: 0.5rem;
}

.person-records-list {
    max-height: 250px;
    overflow-y: auto;
}

.person-record-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: rgba(0,0,0,0.02);
    border-radius: 8px;
    border-left: 3px solid var(--camera-primary);
    transition: var(--camera-transition);
}

.person-record-item:hover {
    background-color: rgba(0,123,255,0.05);
    transform: translateX(5px);
}

.record-count {
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
}

.record-time {
    font-size: 0.8rem;
}

.record-image-btn {
    border-radius: 50%;
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-records-state {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .camera-title {
        font-size: 1.5rem;
    }
    
    .control-grid {
        max-width: 180px;
    }
    
    .control-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .info-value {
        text-align: left;
        max-width: 100%;
        margin-top: 0.25rem;
    }
}

@media (max-width: 768px) {
    .camera-header-card {
        padding: 0.75rem 1rem;
    }
    
    .camera-title {
        font-size: 1.25rem;
    }
    
    .camera-location {
        font-size: 0.85rem;
    }
    
    .stream-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .camera-control-btn {
        font-size: 0.8rem;
        padding: 0.3rem 0.8rem;
    }
}
