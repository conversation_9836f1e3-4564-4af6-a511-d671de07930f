<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid py-3">
    <!-- 返回按钮和操作按钮 -->
    <div class="mb-3 d-flex justify-content-between align-items-center">
        <a href="${pageContext.request.contextPath}/camera/list" class="btn btn-outline-primary btn-sm camera-action-btn">
            <i class="bi bi-arrow-left"></i> 返回摄像头列表
        </a>
        <button class="btn btn-outline-danger btn-sm camera-action-btn" id="deleteCameraBtn" data-camera-id="${camera.id}" data-camera-name="${camera.name}">
            <i class="bi bi-trash me-1"></i> 删除摄像头
        </button>
    </div>

    <!-- 摄像头标题 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="camera-header-card">
                <h2 class="camera-title">
                    <span class="camera-status ${camera.status == 1 ? 'camera-online' : 'camera-offline'}"></span>
                    ${camera.name}
                    <small class="text-muted fs-6 ms-2">${camera.brand} ${camera.model}</small>
                </h2>
                <p class="camera-location text-muted mb-0">
                    <i class="bi bi-geo-alt me-1"></i> ${camera.location}
                    <c:if test="${camera.room != null}">
                        <span class="mx-2">|</span>
                        <i class="bi bi-building me-1"></i> ${camera.room.roomNumber} (${camera.room.floorNumber}楼)
                    </c:if>
                </p>
            </div>
        </div>
    </div>
    
    <!-- 摄像头内容 -->
    <div class="row">
        <!-- 左侧：视频流和控制面板 -->
        <div class="col-lg-8 mb-3">
            <!-- 视频流 -->
            <div class="card camera-stream-card mb-3">
                <div class="card-header bg-gradient-primary p-2">
                    <h5 class="card-title mb-0 text-white">
                        <i class="bi bi-camera-video me-2"></i>视频流
                    </h5>
                </div>
                <div class="card-body p-2">
                    <div class="camera-stream-container">
                        <c:choose>
                            <c:when test="${camera.status == 1}">
                                <img src="${pageContext.request.contextPath}/static/images/camera-stream.jpg" alt="摄像头视频流" class="stream-image">
                            </c:when>
                            <c:otherwise>
                                <div class="stream-overlay">
                                    <i class="bi bi-camera-video-off mb-2 fs-1 text-muted"></i>
                                    <h5 class="text-muted">摄像头离线</h5>
                                    <p class="text-muted mb-0">请连接摄像头后查看视频流</p>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="card-footer bg-light p-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="status-info">
                            <span class="badge ${camera.status == 1 ? 'bg-success' : 'bg-danger'} status-badge">
                                ${camera.status == 1 ? '在线' : '离线'}
                            </span>
                            <c:if test="${camera.status == 1}">
                                <small class="text-muted ms-2">最后在线: ${camera.lastOnlineTime}</small>
                            </c:if>
                        </div>
                        <div class="stream-actions">
                            <c:choose>
                                <c:when test="${camera.status == 0}">
                                    <button id="connectBtn" class="btn btn-success btn-sm camera-control-btn" data-camera-id="${camera.id}" data-action="connect">
                                        <i class="bi bi-wifi me-1"></i> 连接摄像头
                                    </button>
                                </c:when>
                                <c:otherwise>
                                    <button id="connectBtn" class="btn btn-danger btn-sm camera-control-btn" data-camera-id="${camera.id}" data-action="disconnect">
                                        <i class="bi bi-wifi-off me-1"></i> 断开摄像头
                                    </button>
                                    <button id="viewStreamBtn" class="btn btn-primary btn-sm camera-control-btn ms-1" data-camera-id="${camera.id}">
                                        <i class="bi bi-play-circle me-1"></i> 全屏查看
                                    </button>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 控制面板 -->
            <div class="card camera-control-card">
                <div class="card-header bg-gradient-secondary p-2">
                    <h5 class="card-title mb-0 text-white">
                        <i class="bi bi-joystick me-2"></i>控制面板
                    </h5>
                </div>
                <div class="card-body p-3">
                    <div class="camera-control-panel">
                        <div class="control-grid">
                            <!-- 上方向 -->
                            <div class="control-row justify-content-center mb-2">
                                <button class="btn btn-outline-primary control-btn" data-camera-id="${camera.id}" data-action="tilt_up">
                                    <i class="bi bi-arrow-up"></i>
                                </button>
                            </div>
                            <!-- 左中右方向 -->
                            <div class="control-row justify-content-center mb-2">
                                <button class="btn btn-outline-primary control-btn" data-camera-id="${camera.id}" data-action="pan_left">
                                    <i class="bi bi-arrow-left"></i>
                                </button>
                                <button class="btn btn-outline-secondary control-btn mx-2" data-camera-id="${camera.id}" data-action="home">
                                    <i class="bi bi-house"></i>
                                </button>
                                <button class="btn btn-outline-primary control-btn" data-camera-id="${camera.id}" data-action="pan_right">
                                    <i class="bi bi-arrow-right"></i>
                                </button>
                            </div>
                            <!-- 下方向 -->
                            <div class="control-row justify-content-center mb-3">
                                <button class="btn btn-outline-primary control-btn" data-camera-id="${camera.id}" data-action="tilt_down">
                                    <i class="bi bi-arrow-down"></i>
                                </button>
                            </div>
                            <!-- 缩放控制 -->
                            <div class="control-row justify-content-center">
                                <button class="btn btn-outline-info control-btn zoom-btn" data-camera-id="${camera.id}" data-action="zoom_out" title="缩小">
                                    <i class="bi bi-dash-lg"></i>
                                </button>
                                <span class="mx-2 text-muted">缩放</span>
                                <button class="btn btn-outline-info control-btn zoom-btn" data-camera-id="${camera.id}" data-action="zoom_in" title="放大">
                                    <i class="bi bi-plus-lg"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧：摄像头信息和人员记录 -->
        <div class="col-lg-4">
            <!-- 摄像头信息 -->
            <div class="card camera-info-card mb-3">
                <div class="card-header bg-gradient-info p-2">
                    <h5 class="card-title mb-0 text-white">
                        <i class="bi bi-info-circle me-2"></i>摄像头信息
                    </h5>
                </div>
                <div class="card-body p-2">
                    <div class="camera-info-list">
                        <div class="info-item">
                            <div class="info-label">
                                <i class="bi bi-router me-2 text-primary"></i>IP地址
                            </div>
                            <div class="info-value">${camera.ipAddress}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="bi bi-ethernet me-2 text-primary"></i>端口
                            </div>
                            <div class="info-value">${camera.port}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="bi bi-link-45deg me-2 text-primary"></i>RTSP URL
                            </div>
                            <div class="info-value">
                                <small class="text-muted text-break">${camera.rtspUrl}</small>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="bi bi-tag me-2 text-primary"></i>品牌
                            </div>
                            <div class="info-value">${camera.brand}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="bi bi-cpu me-2 text-primary"></i>型号
                            </div>
                            <div class="info-value">${camera.model}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="bi bi-geo-alt me-2 text-primary"></i>位置
                            </div>
                            <div class="info-value">${camera.location}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="bi bi-building me-2 text-primary"></i>所属房间
                            </div>
                            <div class="info-value">
                                <c:choose>
                                    <c:when test="${camera.room != null}">
                                        <span class="badge bg-light text-dark">${camera.room.roomNumber} (${camera.room.floorNumber}楼)</span>
                                    </c:when>
                                    <c:otherwise>
                                        <span class="text-muted">未分配房间</span>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="bi bi-calendar-plus me-2 text-primary"></i>创建时间
                            </div>
                            <div class="info-value">
                                <small class="text-muted">${camera.createTime}</small>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="bi bi-clock-history me-2 text-primary"></i>最后更新
                            </div>
                            <div class="info-value">
                                <small class="text-muted">${camera.updateTime}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 人员记录 -->
            <c:if test="${camera.roomId > 0}">
                <div class="card camera-person-card">
                    <div class="card-header bg-gradient-success p-2">
                        <h5 class="card-title mb-0 text-white">
                            <i class="bi bi-people me-2"></i>人员记录
                        </h5>
                    </div>
                    <div class="card-body p-2">
                        <c:choose>
                            <c:when test="${not empty personRecords}">
                                <!-- 当前人数 -->
                                <div class="current-count-card ${latestRecord.personCount > 0 ? 'bg-success' : 'bg-secondary'} text-white mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0">当前人数</h6>
                                            <small class="opacity-75">最后更新: ${latestRecord.recordTime}</small>
                                        </div>
                                        <div class="person-count-display">
                                            <span class="count-number">${latestRecord.personCount}</span>
                                            <small class="d-block">人</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- 人数统计图表 -->
                                <div class="chart-container mb-3">
                                    <canvas id="personCountChart"></canvas>
                                </div>

                                <!-- 历史记录 -->
                                <h6 class="section-title mb-2">
                                    <i class="bi bi-clock-history me-1"></i>历史记录
                                </h6>
                                <div class="person-records-list">
                                    <c:forEach items="${personRecords}" var="record" varStatus="status" begin="0" end="4">
                                        <div class="person-record-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="record-info">
                                                    <div class="record-count">
                                                        <i class="bi bi-person-fill me-1 text-primary"></i>
                                                        <strong>${record.personCount} 人</strong>
                                                    </div>
                                                    <small class="record-time text-muted">${record.recordTime}</small>
                                                </div>
                                                <c:if test="${record.imageUrl != null}">
                                                    <button class="btn btn-sm btn-outline-primary record-image-btn">
                                                        <i class="bi bi-image"></i>
                                                    </button>
                                                </c:if>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div class="empty-records-state">
                                    <div class="text-center py-3">
                                        <i class="bi bi-person-x fs-1 text-muted mb-2 d-block"></i>
                                        <h6 class="text-muted">暂无人员记录</h6>
                                        <p class="text-muted mb-0 small">摄像头连接后将自动记录人员信息</p>
                                    </div>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </c:if>
        </div>
    </div>
</div>
